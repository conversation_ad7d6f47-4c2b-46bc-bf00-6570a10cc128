const fs = require('fs');
const path = require('path');

/**
 * 环境配置加载器
 * 根据NODE_ENV环境变量加载对应的配置文件
 */
function loadConfig() {
  const env = process.env.NODE_ENV || 'development';
  
  let configFile;
  
  switch (env) {
    case 'production':
      configFile = 'config.production.json';
      break;
    case 'test':
      configFile = 'config.test.json';
      break;
    case 'development':
    default:
      configFile = 'config.json';
      break;
  }
  
  const configPath = path.join(__dirname, configFile);
  
  try {
    if (fs.existsSync(configPath)) {
      const config = require(configPath);
      console.log(`🔧 加载配置文件: ${configFile} (环境: ${env})`);
      return config;
    } else {
      console.warn(`⚠️  配置文件不存在: ${configFile}, 使用默认配置`);
      return require('./config.json');
    }
  } catch (error) {
    console.error(`❌ 加载配置文件失败: ${error.message}`);
    throw error;
  }
}

module.exports = loadConfig();
