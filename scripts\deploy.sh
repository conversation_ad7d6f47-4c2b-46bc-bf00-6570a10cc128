#!/bin/bash

# 河南农业大学小程序后端部署脚本

ENV=${1:-production}

echo "🚀 部署河南农业大学小程序后端到 $ENV 环境..."

case $ENV in
  "production")
    echo "📦 部署到生产环境..."
    pm2 deploy ecosystem.config.js production
    ;;
  "staging")
    echo "📦 部署到测试环境..."
    pm2 deploy ecosystem.config.js staging
    ;;
  "setup")
    echo "🔧 初始化部署环境..."
    pm2 deploy ecosystem.config.js production setup
    ;;
  *)
    echo "❌ 无效的环境参数: $ENV"
    echo "使用方法: ./deploy.sh [production|staging|setup]"
    exit 1
    ;;
esac

echo "✅ 部署完成"
