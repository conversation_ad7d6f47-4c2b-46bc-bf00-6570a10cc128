const mysql = require('mysql2/promise');
require('dotenv').config();

const config = require('./index');
const logger = require('../utils/logger');


// 数据库连接配置
const dbConfig = {
  host: config.host,
  port:  3306,
  user: config.user,
  password: config.password,
  database: config.database,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4',
};


// 创建连接池
const pool = mysql.createPool(dbConfig);

// 测试数据库连接
async function testConnection() {
  try {
    logger.database('正在尝试连接数据库...');
    const connection = await pool.getConnection();
    logger.database('数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    logger.error('数据库连接失败:', error.message);
    logger.debug('错误代码:', error.code);
    logger.debug('完整错误:', error);
    return false;
  }
}

// 执行查询
async function query(sql, params = []) {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    logger.error('数据库查询错误:', error.message);
    logger.debug('SQL:', sql);
    logger.debug('参数:', params);
    throw error;
  }
}

// 执行事务
async function transaction(callback) {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

module.exports = {
  pool,
  query,
  transaction,
  testConnection
};
