const axios = require('axios');
const { query, transaction } = require('../config/database');
const { logUserAction } = require('./logService');
const config = require('../config/config.json');

// 河南农业大学OAuth配置
const OAUTH_CONFIG = {
  baseURL: config.henau_oauth_url,
  appid: config.henau_appid,
  secret: config.henau_secret,
  grant_type: 'authorization_code'
};

/**
 * 通过mp_code获取用户信息
 * @param {string} mpCode - 微信小程序code
 * @param {string} clientIP - 客户端IP地址
 * @param {string} userAgent - 用户代理
 * @returns {Object} 用户信息或错误信息
 */
async function getUserInfoByMpCode(mpCode, clientIP, userAgent) {
  try {
    // 构建请求URL
    const requestURL = `${OAUTH_CONFIG.baseURL}?appid=${OAUTH_CONFIG.appid}&secret=${OAUTH_CONFIG.secret}&mp_code=${mpCode}&grant_type=${OAUTH_CONFIG.grant_type}`;
    
    console.log('请求用户信息:', { mpCode, clientIP });

    // 向河南农业大学OAuth服务器请求用户信息
    const response = await axios.get(requestURL, {
      timeout: 10000,
      headers: {
        'User-Agent': 'HENAU-MiniProgram-Backend/1.0'
      }
    });

    const responseData = response.data;
    
    // 检查响应状态
    if (responseData.status === 'error') {
      // 记录认证失败日志
      await logUserAction({
        henau_openid: null,
        action: 'auth_failed',
        description: `用户认证失败: ${responseData.data.errmsg}`,
        ip_address: clientIP,
        user_agent: userAgent,
        request_data: { mp_code: mpCode },
        response_data: responseData,
        status: 'error',
        error_message: responseData.data.errmsg
      }).catch(err => console.error('日志记录失败:', err));

      return {
        success: false,
        error: responseData.data
      };
    }

    // 认证成功，处理用户信息
    const userInfo = responseData.data;
    
    // 保存或更新用户信息到数据库
    const savedUser = await saveOrUpdateUser(userInfo);
    
    // 记录登录成功日志
    await logUserAction({
      henau_openid: userInfo.henau_openid,
      action: 'login_success',
      description: '用户登录成功',
      ip_address: clientIP,
      user_agent: userAgent,
      request_data: { mp_code: mpCode },
      response_data: {
        henau_openid: userInfo.henau_openid,
        user_name: userInfo.user_name,
        user_status: userInfo.user_status
      },
      status: 'success'
    }).catch(err => console.error('日志记录失败:', err));

    return {
      success: true,
      data: {
        ...userInfo,
        user_id: savedUser.id
      }
    };

  } catch (error) {
    console.error('获取用户信息失败:', error);
    
    // 记录系统错误日志
    await logUserAction({
      henau_openid: null,
      action: 'auth_error',
      description: '获取用户信息系统错误',
      ip_address: clientIP,
      user_agent: userAgent,
      request_data: { mp_code: mpCode },
      status: 'error',
      error_message: error.message
    }).catch(err => console.error('日志记录失败:', err));

    return {
      success: false,
      error: {
        errcode: 50002,
        errmsg: 'Failed to get user info from OAuth server'
      }
    };
  }
}

/**
 * 保存或更新用户信息到数据库
 * @param {Object} userInfo - 用户信息
 * @returns {Object} 保存的用户信息
 */
async function saveOrUpdateUser(userInfo) {
  return await transaction(async (connection) => {
    const now = new Date();

    // 判断是校内用户还是校外用户
    const isInternalUser = userInfo.henau_openid && userInfo.user_status !== 6;

    if (isInternalUser) {
      // 校内用户：使用henau_openid作为唯一标识
      const [existingUsers] = await connection.execute(
        'SELECT id, henau_openid FROM users WHERE henau_openid = ?',
        [userInfo.henau_openid]
      );

      if (existingUsers.length > 0) {
        // 更新现有校内用户信息
        await connection.execute(`
          UPDATE users SET
            mp_openid = ?,
            user_name = ?,
            user_number = ?,
            user_section = ?,
            user_phone = ?,
            user_nickname = ?,
            user_avatar_url = ?,
            user_status = ?,
            henau_bind_url = ?,
            session_key = ?,
            updated_at = ?,
            last_login_at = ?
          WHERE henau_openid = ?
        `, [
          userInfo.mp_openid,
          userInfo.user_name || null,
          userInfo.user_number || null,
          userInfo.user_section || null,
          userInfo.user_phone || null,
          userInfo.user_nickname || null,
          userInfo.user_avatar_url || null,
          userInfo.user_status,
          userInfo.henau_bind_url || null,
          userInfo.session_key || null,
          now,
          now,
          userInfo.henau_openid
        ]);

        return { id: existingUsers[0].id, ...userInfo };
      } else {
        // 创建新的校内用户
        const [result] = await connection.execute(`
          INSERT INTO users (
            henau_openid, mp_openid, user_name, user_number, user_section,
            user_phone, user_nickname, user_avatar_url, user_status,
            henau_bind_url, session_key, created_at, updated_at, last_login_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          userInfo.henau_openid,
          userInfo.mp_openid,
          userInfo.user_name || null,
          userInfo.user_number || null,
          userInfo.user_section || null,
          userInfo.user_phone || null,
          userInfo.user_nickname || null,
          userInfo.user_avatar_url || null,
          userInfo.user_status,
          userInfo.henau_bind_url || null,
          userInfo.session_key || null,
          now,
          now,
          now
        ]);

        return { id: result.insertId, ...userInfo };
      }
    } else {
      // 校外用户：使用mp_openid作为唯一标识
      const [existingUsers] = await connection.execute(
        'SELECT id, mp_openid FROM users WHERE mp_openid = ? AND henau_openid IS NULL',
        [userInfo.mp_openid]
      );

      if (existingUsers.length > 0) {
        // 更新现有校外用户信息
        await connection.execute(`
          UPDATE users SET
            henau_bind_url = ?,
            session_key = ?,
            user_status = ?,
            updated_at = ?,
            last_login_at = ?
          WHERE mp_openid = ? AND henau_openid IS NULL
        `, [
          userInfo.henau_bind_url || null,
          userInfo.session_key || null,
          userInfo.user_status,
          now,
          now,
          userInfo.mp_openid
        ]);

        return { id: existingUsers[0].id, ...userInfo };
      } else {
        // 创建新的校外用户
        const [result] = await connection.execute(`
          INSERT INTO users (
            henau_openid, mp_openid, user_name, user_number, user_section,
            user_phone, user_nickname, user_avatar_url, user_status,
            henau_bind_url, session_key, created_at, updated_at, last_login_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          null, // henau_openid为null
          userInfo.mp_openid,
          null, // user_name为null
          null, // user_number为null
          null, // user_section为null
          null, // user_phone为null
          null, // user_nickname为null
          null, // user_avatar_url为null
          userInfo.user_status,
          userInfo.henau_bind_url || null,
          userInfo.session_key || null,
          now,
          now,
          now
        ]);

        return { id: result.insertId, ...userInfo };
      }
    }
  });
}

module.exports = {
  getUserInfoByMpCode,
  saveOrUpdateUser
};
