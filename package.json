{"name": "henau-mini-program-backend", "version": "1.0.0", "description": "河南农业大学小程序后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:dev": "pm2 start ecosystem.config.js --env development", "pm2:test": "pm2 start ecosystem.config.js --only henau-miniprogram-backend-test", "pm2:prod": "pm2 start ecosystem.config.js --only henau-miniprogram-backend-prod", "pm2:stop": "pm2 stop all", "pm2:stop:test": "pm2 stop henau-miniprogram-backend-test", "pm2:stop:prod": "pm2 stop henau-miniprogram-backend-prod", "pm2:restart": "pm2 restart all", "pm2:restart:test": "pm2 restart henau-miniprogram-backend-test", "pm2:restart:prod": "pm2 restart henau-miniprogram-backend-prod", "pm2:reload": "pm2 reload all", "pm2:delete": "pm2 delete all", "pm2:status": "pm2 status", "pm2:logs": "pm2 logs", "pm2:logs:test": "pm2 logs henau-miniprogram-backend-test", "pm2:logs:prod": "pm2 logs henau-miniprogram-backend-prod", "pm2:monit": "pm2 monit", "deploy:production": "pm2 deploy ecosystem.config.js production", "deploy:test": "pm2 deploy ecosystem.config.js test", "deploy:setup:prod": "pm2 deploy ecosystem.config.js production setup", "deploy:setup:test": "pm2 deploy ecosystem.config.js test setup"}, "keywords": ["nodejs", "express", "mysql", "henau", "miniprogram"], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "mysql2": "^3.6.5", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "pm2": "^5.3.0"}}