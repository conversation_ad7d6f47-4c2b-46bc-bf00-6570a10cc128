const config = require('../config');

/**
 * 日志管理器
 * 根据环境配置控制日志输出
 */
class Logger {
  constructor() {
    this.logLevel = config.log_level || 'info';
    this.enableConsoleLog = config.enable_console_log !== false;
    this.enableFileLog = config.enable_file_log !== false;
    this.environment = config.environment || 'development';
    
    // 日志级别优先级
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
  }

  /**
   * 检查是否应该输出日志
   */
  shouldLog(level) {
    return this.levels[level] <= this.levels[this.logLevel];
  }

  /**
   * 格式化日志消息
   */
  formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const env = this.environment.toUpperCase();
    return `[${timestamp}] [${env}] [${level.toUpperCase()}] ${message}`;
  }

  /**
   * 错误日志
   */
  error(message, ...args) {
    if (this.shouldLog('error')) {
      const formattedMessage = this.formatMessage('error', message, ...args);
      if (this.enableConsoleLog) {
        console.error(formattedMessage, ...args);
      }
    }
  }

  /**
   * 警告日志
   */
  warn(message, ...args) {
    if (this.shouldLog('warn')) {
      const formattedMessage = this.formatMessage('warn', message, ...args);
      if (this.enableConsoleLog) {
        console.warn(formattedMessage, ...args);
      }
    }
  }

  /**
   * 信息日志
   */
  info(message, ...args) {
    if (this.shouldLog('info')) {
      const formattedMessage = this.formatMessage('info', message, ...args);
      if (this.enableConsoleLog) {
        console.log(formattedMessage, ...args);
      }
    }
  }

  /**
   * 调试日志
   */
  debug(message, ...args) {
    if (this.shouldLog('debug')) {
      const formattedMessage = this.formatMessage('debug', message, ...args);
      if (this.enableConsoleLog) {
        console.log(formattedMessage, ...args);
      }
    }
  }

  /**
   * 数据库连接日志（关键信息，总是显示）
   */
  database(message, ...args) {
    const formattedMessage = this.formatMessage('database', message, ...args);
    if (this.enableConsoleLog) {
      console.log(`🔧 ${formattedMessage}`, ...args);
    }
  }

  /**
   * 服务器启动日志（关键信息，总是显示）
   */
  server(message, ...args) {
    const formattedMessage = this.formatMessage('server', message, ...args);
    if (this.enableConsoleLog) {
      console.log(`🚀 ${formattedMessage}`, ...args);
    }
  }

  /**
   * API请求日志
   */
  request(message, ...args) {
    if (this.shouldLog('info')) {
      const formattedMessage = this.formatMessage('request', message, ...args);
      if (this.enableConsoleLog) {
        console.log(`📡 ${formattedMessage}`, ...args);
      }
    }
  }

  /**
   * 用户操作日志（关键信息，总是记录）
   */
  userAction(message, ...args) {
    const formattedMessage = this.formatMessage('user', message, ...args);
    if (this.enableConsoleLog) {
      console.log(`👤 ${formattedMessage}`, ...args);
    }
  }
}

// 创建全局日志实例
const logger = new Logger();

module.exports = logger;
