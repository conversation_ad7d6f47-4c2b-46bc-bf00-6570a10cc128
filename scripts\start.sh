#!/bin/bash

# 河南农业大学小程序后端启动脚本

echo "🚀 启动河南农业大学小程序后端服务..."

# 创建日志目录
mkdir -p logs

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2未安装，正在安装..."
    npm install -g pm2
fi

# 检查Node.js版本
NODE_VERSION=$(node -v)
echo "📦 Node.js版本: $NODE_VERSION"

# 安装依赖
echo "📥 安装依赖包..."
npm install

# 启动应用
echo "🔄 启动应用..."
pm2 start ecosystem.config.js --env production

# 显示状态
echo "📊 应用状态:"
pm2 status

# 显示日志
echo "📝 实时日志 (按Ctrl+C退出):"
pm2 logs henau-miniprogram-backend --lines 50
