module.exports = {
  apps: [
    {
      // 应用基本信息
      name: 'henau-miniprogram-backend',
      script: 'app.js',
      
      // 运行环境
      cwd: './',
      node_args: '--max-old-space-size=1024',
      
      // 实例配置
      instances: 'max', // 使用所有CPU核心，也可以设置具体数字如 2
      exec_mode: 'cluster', // 集群模式
      
      // 自动重启配置
      autorestart: true,
      watch: false, // 生产环境建议关闭文件监听
      max_memory_restart: '1G', // 内存超过1G时重启
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 环境变量
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },

      // 开发环境变量
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000
      },

      // 测试环境变量
      env_test: {
        NODE_ENV: 'test',
        PORT: 3001
      },

      // 预生产环境变量
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3002
      },
      
      // 进程管理配置
      min_uptime: '10s', // 最小运行时间
      max_restarts: 10, // 最大重启次数
      restart_delay: 4000, // 重启延迟
      
      // 健康检查
      health_check_grace_period: 3000,
      
      // 优雅关闭
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // 其他配置
      ignore_watch: [
        'node_modules',
        'logs',
        '.git',
        '*.log'
      ],

      // 时间配置
      time: true
    },

    // 测试环境应用配置
    {
      name: 'henau-miniprogram-backend-test',
      script: 'app.js',
      cwd: './',
      instances: 1, // 测试环境使用单实例
      exec_mode: 'fork',
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      log_file: './logs/test-combined.log',
      out_file: './logs/test-out.log',
      error_file: './logs/test-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      env: {
        NODE_ENV: 'test',
        PORT: 3001
      },
      min_uptime: '10s',
      max_restarts: 5,
      restart_delay: 2000
    },

    // 生产环境应用配置
    {
      name: 'henau-miniprogram-backend-prod',
      script: 'app.js',
      cwd: './',
      instances: 'max', // 生产环境使用所有CPU核心
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      log_file: './logs/prod-combined.log',
      out_file: './logs/prod-out.log',
      error_file: './logs/prod-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000
    }
  ],

  // 部署配置
  deploy: {
    // 生产环境部署
    production: {
      user: 'root',
      host: '*************',
      ref: 'origin/production',
      repo: 'https://git.henau.edu.cn/mytx666/newstudentknowledge_backend.git',
      path: '/var/www/henau-miniprogram-backend-prod',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 start ecosystem.config.js --only henau-miniprogram-backend-prod',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },

    // 测试环境部署
    test: {
      user: 'root',
      host: '*************',
      ref: 'origin/test',
      repo: 'https://git.henau.edu.cn/mytx666/newstudentknowledge_backend.git',
      path: '/var/www/henau-miniprogram-backend-test',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 start ecosystem.config.js --only henau-miniprogram-backend-test',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
