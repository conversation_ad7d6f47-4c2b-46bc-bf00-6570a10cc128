const { query } = require('../config/database');

/**
 * 记录用户操作日志
 * @param {Object} logData - 日志数据
 * @param {string} logData.henau_openid - 用户唯一标识（可为null）
 * @param {string} logData.action - 操作类型
 * @param {string} logData.description - 操作描述
 * @param {string} logData.ip_address - IP地址
 * @param {string} logData.user_agent - 用户代理
 * @param {Object} logData.request_data - 请求数据（可选）
 * @param {Object} logData.response_data - 响应数据（可选）
 * @param {string} logData.status - 操作状态：success, error, warning
 * @param {string} logData.error_message - 错误信息（可选）
 */
async function logUserAction(logData) {
  try {
    const {
      henau_openid = null,
      action,
      description,
      ip_address,
      user_agent = null,
      request_data = null,
      response_data = null,
      status = 'success',
      error_message = null
    } = logData;

    // 验证必需字段
    if (!action || !description || !ip_address) {
      console.error('日志记录失败：缺少必需字段', logData);
      return false;
    }

    // 插入日志记录
    // created_at 字段使用 NOW() 插入当前时间，这与“去除时间过滤”不冲突
    await query(`
      INSERT INTO user_logs (
        henau_openid, action, description, ip_address, user_agent,
        request_data, response_data, status, error_message, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      henau_openid,
      action,
      description,
      ip_address,
      user_agent,
      request_data ? JSON.stringify(request_data) : null,
      response_data ? JSON.stringify(response_data) : null,
      status,
      error_message
    ]);

    console.log(`✅ 日志记录成功: ${action} - ${description}`);
    return true;
  } catch (error) {
    console.error('❌ 日志记录失败:', error);
    return false;
  }
}

/**
 * 获取用户操作日志 (已移除时间过滤)
 * @param {string} henauOpenid - 用户唯一标识
 * @param {Object} options - 查询选项
 * @param {number} options.page - 页码（从1开始）
 * @param {number} options.limit - 每页记录数
 * @param {string} options.action - 操作类型过滤
 * @param {string} options.status - 状态过滤
 * @returns {Object} 日志列表和分页信息
 */
async function getUserLogs(henauOpenid, options = {}) {
  try {
    const {
      page = 1,
      limit = 20,
      action = null,
      status = null
      // startDate 和 endDate 已移除
    } = options;

    const offset = (page - 1) * limit;
    let whereConditions = ['henau_openid = ?'];
    let queryParams = [henauOpenid];

    // 添加过滤条件
    if (action) {
      whereConditions.push('action = ?');
      queryParams.push(action);
    }

    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    // 移除了 startDate 和 endDate 的条件

    const whereClause = whereConditions.join(' AND ');

    // 获取总记录数
    const countResult = await query(
      `SELECT COUNT(*) as total FROM user_logs WHERE ${whereClause}`,
      queryParams
    );
    const total = countResult[0].total;

    // 获取日志列表
    const logs = await query(`
      SELECT 
        id, action, description, ip_address, user_agent,
        request_data, response_data, status, error_message, created_at
      FROM user_logs 
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]);

    // 解析JSON字段
    const processedLogs = logs.map(log => ({
      ...log,
      request_data: log.request_data ? JSON.parse(log.request_data) : null,
      response_data: log.response_data ? JSON.parse(log.response_data) : null
    }));

    return {
      success: true,
      data: {
        logs: processedLogs,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    };
  } catch (error) {
    console.error('获取用户日志失败:', error);
    return {
      success: false,
      error: {
        errcode: 50003,
        errmsg: 'Failed to get user logs'
      }
    };
  }
}

/**
 * 获取系统日志统计 (已移除时间过滤)
 * @param {Object} options - 查询选项 (startDate 和 endDate 已移除，不再生效)
 * @returns {Object} 统计信息
 */
async function getLogStatistics(options = {}) {
  try {
    // startDate 和 endDate 已移除，默认值不再生效
    // const {
    //   startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 默认7天前
    //   endDate = new Date()
    // } = options;

    // 按操作类型统计
    const actionStats = await query(`
      SELECT action, COUNT(*) as count
      FROM user_logs
      GROUP BY action
      ORDER BY count DESC
    `); // 移除了 WHERE 子句和参数

    // 按状态统计
    const statusStats = await query(`
      SELECT status, COUNT(*) as count
      FROM user_logs
      GROUP BY status
    `); // 移除了 WHERE 子句和参数

    // 按日期统计
    // 注意：此查询现在将统计所有历史记录的每日数据，如果数据量大，可能会影响性能。
    const dailyStats = await query(`
      SELECT DATE(created_at) as date, COUNT(*) as count
      FROM user_logs
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `); // 移除了 WHERE 子句和参数

    return {
      success: true,
      data: {
        actionStats,
        statusStats,
        dailyStats,
        period: {
          // 明确表示统计是针对所有时间段的
          description: '所有历史记录'
        }
      }
    };
  } catch (error) {
    console.error('获取日志统计失败:', error);
    return {
      success: false,
      error: {
        errcode: 50004,
        errmsg: 'Failed to get log statistics'
      }
    };
  }
}

/**
 * 获取用户访问统计 (已移除时间过滤)
 * @param {Object} options - 查询选项 (startDate 和 endDate 已移除，不再生效)
 * @returns {Object} 用户访问统计信息
 */
async function getUserAccessStatistics(options = {}) {
  try {


    // 总用户数 (此查询本身就没有时间限制)
    const totalUsersResult = await query(`
      SELECT COUNT(DISTINCT henau_openid) as total_users
      FROM users
    `);
    const totalUsers = totalUsersResult[0].total_users;

    // 活跃用户数（所有时间段内有登录记录的用户，包括校内和校外用户）
    const activeUsersResult = await query(`
      SELECT COUNT(DISTINCT
        CASE
          WHEN henau_openid IS NOT NULL THEN henau_openid
          ELSE CONCAT('external_', ip_address)
        END
      ) as active_users
      FROM user_logs
      WHERE action = 'login_success'
    `); // 移除了 AND created_at BETWEEN ? AND ?
    const activeUsers = activeUsersResult[0].active_users;

    // 新用户数（所有时间段内注册的用户）
    const newUsersResult = await query(`
      SELECT COUNT(*) as new_users
      FROM users
    `); // 移除了 WHERE created_at BETWEEN ? AND ?
    const newUsers = newUsersResult[0].new_users;

    // 总访问次数（登录成功次数），此查询本身就没有时间限制
    const totalVisitsResult = await query(`
      SELECT COUNT(*) as total_visits
      FROM user_logs
      WHERE action = 'login_success'
    `);
    const totalVisits = totalVisitsResult[0].total_visits;

    // 按日期统计访问用户数（包括校内和校外用户）
    // 注意：此查询现在将统计所有历史记录的每日数据，如果数据量大，可能会影响性能。
    const dailyActiveUsers = await query(`
      SELECT
        DATE(created_at) as date,
        COUNT(DISTINCT
          CASE
            WHEN henau_openid IS NOT NULL THEN henau_openid
            ELSE CONCAT('external_', ip_address)
          END
        ) as active_users,
        COUNT(*) as total_visits
      FROM user_logs
      WHERE action = 'login_success'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `); // 移除了 AND created_at BETWEEN ? AND ?

    return {
      success: true,
      data: {
        summary: {
          totalUsers,
          activeUsers,
          newUsers,
          totalVisits,
          period: {
            // 明确表示统计是针对所有时间段的
            description: '所有历史记录'
          }
        },
      }
    };
  } catch (error) {
    console.error('获取用户访问统计失败:', error);
    return {
      success: false,
      error: {
        errcode: 50008,
        errmsg: 'Failed to get user access statistics'
      }
    };
  }
}

module.exports = {
  logUserAction,
  getUserLogs,
  getLogStatistics,
  getUserAccessStatistics
};
