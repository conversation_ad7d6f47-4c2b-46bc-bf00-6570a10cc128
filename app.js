const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const config = require('./config');
const logger = require('./utils/logger');
const { swaggerSpec, swaggerUi } = require('./config/swagger');

const { testConnection } = require('./config/database');
const { getClientIP } = require('./utils/helpers');
const { getUserInfoByMpCode } = require('./services/authService');
const { logUserAction, getUserLogs, getLogStatistics, getUserAccessStatistics } = require('./services/logService');

// 静默模式：覆盖console方法
if (config.silentMode) {
  console.log = () => {};
  console.error = () => {};
  console.warn = () => {};
  console.info = () => {};
}

const app = express();
const PORT = parseInt(config.port) || 3000;

// 基础中间件
app.use(helmet()); // 安全头
app.use(cors()); // 跨域支持
app.use(express.json({ limit: '10mb' })); // JSON解析
app.use(express.urlencoded({ extended: true })); // URL编码解析

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    status: 'error',
    message: 'Too many requests',
    data: {
      errcode: 40002,
      errmsg: 'Rate limit exceeded'
    }
  }
});
app.use(limiter);

// 请求日志中间件
app.use((req, res, next) => {
  const clientIP = getClientIP(req);
  logger.request(`${req.method} ${req.originalUrl} - IP: ${clientIP}`);
  next();
});

// API文档路由
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: '河南农业大学小程序API文档',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    docExpansion: 'list',
    filter: true,
    showExtensions: true,
    showCommonExtensions: true
  }
}));

// API文档JSON格式
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
});

/**
 * @swagger
 * /health:
 *   get:
 *     tags:
 *       - Health
 *     summary: 健康检查接口
 *     description: 检查服务器和数据库连接状态
 *     responses:
 *       200:
 *         description: 服务健康
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthResponse'
 *             example:
 *               status: "success"
 *               message: "成功启动服务"
 *               data:
 *                 timestamp: "2024-01-01T12:00:00.000Z"
 *                 database: "connected"
 *                 version: "1.0.0"
 *                 silentMode: false
 *       500:
 *         description: 服务不健康
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               status: "error"
 *               message: "Service is unhealthy"
 *               data:
 *                 timestamp: "2024-01-01T12:00:00.000Z"
 *                 error: "Database connection failed"
 */
// 健康检查接口
app.get('/health', async (req, res) => {
  try {
    const dbConnected = await testConnection();
    res.json({
      status: 'success',
      message: '成功启动服务',
      data: {
        timestamp: new Date().toISOString(),
        database: dbConnected ? 'connected' : 'disconnected',
        version: '1.0.0',
        silentMode: config.silentMode || false
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Service is unhealthy',
      data: {
        timestamp: new Date().toISOString(),
        error: config.silentMode ? 'Service error' : error.message
      }
    });
  }
});

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     tags:
 *       - Authentication
 *     summary: 用户登录认证
 *     description: 通过微信小程序code进行用户认证，获取用户信息
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *             properties:
 *               code:
 *                 type: string
 *                 description: 微信小程序授权码
 *                 example: "your_mp_code_here"
 *           example:
 *             code: "your_mp_code_here"
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/UserInfo'
 *             example:
 *               status: "success"
 *               message: "Login successful"
 *               data:
 *                 henau_openid: "henau_123456789"
 *                 user_name: "张三"
 *                 user_status: 1
 *                 user_id: 123
 *       400:
 *         description: 登录失败（参数错误或认证失败）
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               parameter_error:
 *                 summary: 参数错误
 *                 value:
 *                   status: "error"
 *                   message: "Invalid parameter: 缺少code参数"
 *                   data:
 *                     errcode: 40003
 *                     errmsg: "参数错误：缺少code参数。正确格式示例：{\"code\": \"your_mp_code_here\"}"
 *               auth_failed:
 *                 summary: 认证失败
 *                 value:
 *                   status: "error"
 *                   message: "Login failed"
 *                   data:
 *                     errcode: 40001
 *                     errmsg: "Invalid authorization code"
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               status: "error"
 *               message: "Internal server error"
 *               data:
 *                 errcode: 50005
 *                 errmsg: "Login service error"
 */
// 用户认证接口
app.post('/api/auth/login', async (req, res) => {
  try {
    const { code } = req.body;
    const clientIP = getClientIP(req);
    const userAgent = req.headers['user-agent'];

    // 验证参数
    if (!code || typeof code !== 'string' || code.trim() === '') {
      const errorMsg = !code ? '缺少code参数' :
                      typeof code !== 'string' ? 'code参数类型错误' :
                      'code参数不能为空';

      // 静默模式下也记录错误日志到服务，但不输出到控制台
      await logUserAction({
        henau_openid: null,
        action: 'login_failed',
        description: `登录失败：${errorMsg}`,
        ip_address: clientIP,
        user_agent: userAgent,
        request_data: req.body,
        status: 'error',
        error_message: errorMsg
      }).catch(() => {}); // 静默模式下忽略日志记录错误

      return res.status(400).json({
        status: 'error',
        message: `Invalid parameter: ${errorMsg}`,
        data: {
          errcode: 40003,
          errmsg: `参数错误：${errorMsg}。正确格式示例：{"code": "your_mp_code_here"}`
        }
      });
    }

    // 获取用户信息
    const result = await getUserInfoByMpCode(code, clientIP, userAgent);

    if (result.success) {
      res.json({
        status: 'success',
        message: 'Login successful',
        data: result.data
      });
    } else {
      res.status(400).json({
        status: 'error',
        message: 'Login failed',
        data: result.error
      });
    }
  } catch (error) {
    // 静默模式下不输出错误到控制台
    if (!config.silentMode) {
      console.error('登录接口错误:', error);
    }
    
    await logUserAction({
      henau_openid: null,
      action: 'login_error',
      description: '登录接口系统错误',
      ip_address: getClientIP(req),
      user_agent: req.headers['user-agent'],
      request_data: req.body,
      status: 'error',
      error_message: config.silentMode ? 'System error' : error.message
    }).catch(() => {});

    res.status(500).json({
      status: 'error',
      message: 'Internal server error',
      data: {
        errcode: 50005,
        errmsg: 'Login service error'
      }
    });
  }
});

/**
 * @swagger
 * /api/users/statistics:
 *   get:
 *     tags:
 *       - Statistics
 *     summary: 获取用户访问统计
 *     description: 获取用户访问统计信息，包括总用户数、活跃用户数、新用户数等
 *     parameters:
 *       - $ref: '#/components/parameters/StartDateParam'
 *       - $ref: '#/components/parameters/EndDateParam'
 *     responses:
 *       200:
 *         description: 成功获取统计信息
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/UserStatistics'
 *             example:
 *               status: "success"
 *               message: "成功检索用户访问统计信息"
 *               data:
 *                 summary:
 *                   totalUsers: 1500
 *                   activeUsers: 800
 *                   newUsers: 50
 *                   totalVisits: 5000
 *                   period:
 *                     description: "所有历史记录"
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               status: "error"
 *               message: "Internal server error"
 *               data:
 *                 errcode: 50008
 *                 errmsg: "无法获取用户访问统计信息"
 */
// 获取用户访问统计接口
app.get('/api/users/statistics', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const options = {
      startDate: startDate ? new Date(startDate) : null,
      endDate: endDate ? new Date(endDate) : null
    };

    const result = await getUserAccessStatistics(options);

    if (result.success) {
      res.json({
        status: 'success',
        message: '成功检索用户访问统计信息',
        data: result.data
      });
    } else {
      res.status(400).json({
        status: 'error',
        message: 'Failed to get user access statistics',
        data: result.error
      });
    }
  } catch (error) {
    if (!config.silentMode) {
      console.error('获取用户访问统计接口错误:', error);
    }
    res.status(500).json({
      status: 'error',
      message: 'Internal server error',
      data: {
        errcode: 50008,
        errmsg: '无法获取用户访问统计信息'
      }
    });
  }
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: 'API endpoint not found',
    data: {
      errcode: 40004,
      errmsg: 'invalid request path'
    }
  });
});

// 全局错误处理
app.use((error, req, res, next) => {
  // 静默模式下不输出错误到控制台
  if (!config.silentMode) {
    console.error('全局错误:', error);
  }

  // JSON解析错误
  if (error.type === 'entity.parse.failed') {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid JSON format',
      data: {
        errcode: 40005,
        errmsg: 'JSON格式错误，请检查请求体格式。注意：JSON中的键名必须使用双引号，不能使用单引号。'
      }
    });
  }

  // 其他错误
  res.status(500).json({
    status: 'error',
    message: 'Internal server error',
    data: {
      errcode: 50000,
      errmsg: 'unexpected server error'
    }
  });
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      logger.error('数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    app.listen(PORT, () => {
      logger.server(`服务器启动成功 - 环境: ${config.environment}`);
      logger.server(`端口: ${PORT}`);
      logger.server(`健康检查: http://localhost:${PORT}/health`);
      logger.server(`API文档: http://localhost:${PORT}/api-docs`);
      logger.server(`API文档JSON: http://localhost:${PORT}/api-docs.json`);
      logger.info(`启动时间: ${new Date().toISOString()}`);
    });
  } catch (error) {
    logger.error('服务器启动失败:', error.message);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  logger.server('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.server('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 启动应用
startServer();
